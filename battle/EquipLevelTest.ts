import { assetsMgr } from "./AssetsMgr"
import { cfg<PERSON>el<PERSON> } from "./cfgHelper"
import { EquipEffectType } from "./constant/Enums"
import { util } from "./utils/Utils"
import nodeXlsx from "node-xlsx"
import xlsx from "json-as-xlsx"

require("./extend/ExtendArray")
assetsMgr.init()
cfgHelper.init()

const args = process.argv.slice(2).filter(arg => !arg.includes("--"))

let cfgs = []
let ary = [
    {id: 1, index: 1},
    {id: 1, index: 2},
    {id: 2, index: 1},
    {id: 2, index: 2},
    {id: 3, index: 1},
    {id: 3, index: 2},
    {id: 3, index: 3},
    {id: 4, index: 1},
    {id: 4, index: 2},
    {id: 4, index: 3},
]

let initMain = ()=>{
    let itemFullPath = "../Config/数值检查_分阶段.xlsx"
    let excelData = nodeXlsx.parse(itemFullPath);
    // Editor.log(itemFullPath, " 解析excel耗时 " + (Date.now() - now) + "ms")
    if (!excelData || excelData.length === 0) {
        return
    }
    let it = excelData[0]
    if (!it) return
    let keyLine = it.data[1]
    let startIndex = 0
    for (let i = 0; i < keyLine.length; i++) {
        if (keyLine[i] == "绿色武器打造次数") {
            startIndex = i
            break
        }
    }
    let eventIndex = 0
    for (let i = 0; i < keyLine.length; i++) {
        if (keyLine[i] == "重要事件") {
            eventIndex = i
            break
        }
    }
 
    let stage = 0
    for (let i = 2; i < it.data.length; i++) {
        let data = it.data[i]
        let res = []
        for (let j = startIndex; j < startIndex + ary.length; j++) {
            let num = data[j]
            if (!num) continue
            let {id, index} = ary[j-startIndex]
            res.push({id, index, num})
        }
        if (res.length > 0) {
            if (data[eventIndex] == "怪物二技能") {
                stage = 1
            }
            cfgs.push({data: res, stage})
        }
    }

    // cfgs = cfgs.slice(0, 2)

    // console.log(cfgs)
}

let testEquipLv = ()=>{

    initMain()

    // cfgs = [
    //     {
    //         data: [
    //             {id: 1, index: 1, num: 38},
    //             {id: 1, index: 2, num: 38},
    //             {id: 2, index: 1, num: 93},
    //             {id: 2, index: 2, num: 93},
    //             // {id: 3, index: 1, num: 10},
    //             // {id: 3, index: 2, num: 10},
    //         ], 
    //         stage: 0,
    //     },
    // ]


    let resultMap = []

    let testCount = 10000
    const roleCnt = 10

    let last = cfgs.last().data

    let getSection = (effect)=>{
        return assetsMgr.getJson("EquipSection").datas.find(({min, max})=>{
            return effect.min == min && effect.max == max
        })?.sec
    }

    let getSecIndex = (lv, pro) => {
        let datas = assetsMgr.getJson("EquipMake").datas.filter(d => d.level == lv)
        let sum = 0
        for (let i = 1; i < datas.length; i++) {
            let times = datas[i].times
            sum += times
            if (pro < sum) {
                return i - 1
            }
        }
        return datas.length - 1
    }

    let errorMap = {}

    let test = ()=>{
        for (let q = 1; q <= 10; q++) {
            let lvData = assetsMgr.getJsonData<EquipLevelCfg>("EquipLevel", q)
            let needs = last.filter(c => c.id == q)
            if (!lvData || needs.length <= 0) continue
            let times = 0
            let TIMES = needs.reduce((a, b) => a + b.num, 0)
            let proMap = {}
            let index = 0
            let maxMap = {}
            while (times < TIMES) {
                let need = needs[index]
                let effects = lvData.effect.filter(effect => {
                    return assetsMgr.getJsonData<EquipEffectCfg>("EquipEffect", effect.id).equipIndex == need.index
                })
                let pro = proMap[need.index] || 0
                let sums = []
                for (let effect of effects) {
                    let seqIndex = getSecIndex(q, pro)
                    let section = getSection(effect)
                    if (!section || !section[seqIndex]) {
                        let key = `${effect.min}-${effect.max}`
                        if (!errorMap[key]) {
                            errorMap[key] = 1
                            console.error("没有配置 EquipLevel ", "id: ", q, "effect.id: ", effect.id, "range: ", key, "index: ", seqIndex)
                        }
                        continue
                    }
                    let seq = section[seqIndex]
                    // console.log(q, times, effect.id, seq)
                    let val = util.random(seq.min, seq.max)
                    let detail = assetsMgr.getJsonData<EquipEffectCfg>("EquipEffect", effect.id)
                    let stage = 1
                    // if (q >= 3) {
                    //     stage = 1
                    // }
                    for (let k = 0; k <= stage; k++) {
                        let _val = val
                        let ratio = 1
                        if (detail.type == EquipEffectType.SKILL) {
                            ratio *= 5
                            if (k == 0) {
                                ratio *= 0.2
                            }
                            else {
                                ratio *= 0.25
                            }
                        }
                        else {
                            if ((need.index == 1 || need.index == 2)) {
                                _val = val - 3
                            }
                            if (k == 0) {
                                ratio *= 0.4
                            }
                            else {
                                ratio *= 0.25
                            }
                        }
                        if (!sums[k]) sums[k] = 0
                        sums[k] += _val * ratio
                    }
                }
                pro++
                proMap[need.index] = pro

                let roleIndex = Math.floor(times / needs.length) % roleCnt
                for (let k = 0; k < sums.length; k++) {
                    if (!maxMap[k]) maxMap[k] = {}  
                    if (!maxMap[k][roleIndex]) maxMap[k][roleIndex] = {}
                    if (!maxMap[k][roleIndex][need.index]) maxMap[k][roleIndex][need.index] = 0
                    if (maxMap[k][roleIndex][need.index] < sums[k]) {
                        maxMap[k] = JSON.parse(JSON.stringify(maxMap[k]))
                        maxMap[k][roleIndex][need.index] = sums[k]
                    }
                }

                // console.log(times, JSON.stringify(maxMap))

                for (let k = 0; k < sums.length; k++) {
                    if (!resultMap[k]) resultMap[k] = {}
                    if (!resultMap[k][q]) resultMap[k][q] = {}
                    if (!resultMap[k][q][times]) resultMap[k][q][times] = []
                    resultMap[k][q][times].push(maxMap[k])
                }
                
                index++
                index %= needs.length

                times++
            }
        }
    }

    for (let i = 0; i < testCount; i++) {
        test()
    }

    // console.log(JSON.stringify(resultMap[1][3], null, 2))

    let json = {
        "等级0": [],
        "等级1": [],
        "等级2": [],
        "等级3": [],
        "等级4": [],
        "等级6": [],
        "等级7": [],
        "等级8": [],
        "等级9": [],
        "平均等级": [],
        "差值": [],

        "宝石1": [],
        "宝石2": [],
        "宝石3": [],
        "宝石4": [],
        "宝石5": [],
        "星尘": [],
    }

    let preExp = 0
    let index = 0
    for (let {data, stage} of cfgs) {
        let cfg = data
        index++
        let maxMap = []
        let costMap = {}
        for (let q = 1; q <= 10; q++) {
            let needs = cfg.filter(c => c.id == q)
            if (needs.length <= 0) continue
            let needSum = needs.reduce((a, b) => a + b.num, 0)
            let datas = resultMap[stage][q][needSum-1]
            for (let i = 0; i < datas.length; i++) {
                let data = datas[i]
                let info = maxMap[i] || {}
                for (let roleIndex in data) {
                    if (!info[roleIndex]) info[roleIndex] = {}
                    for (let needIndex in data[roleIndex]) {
                        if (!info[roleIndex][needIndex]) info[roleIndex][needIndex] = 0
                        let val = data[roleIndex][needIndex]
                        info[roleIndex][needIndex] = Math.max(info[roleIndex][needIndex], val)
                    }
                }
                maxMap[i] = info
            }

            let makeDatas = assetsMgr.getJson("EquipMake").datas.filter(d => d.level == q)
            for (let need of needs) {
                let num = need.num
                for (let i = 1; i <= makeDatas.length; i++) {
                    let costs = makeDatas[i - 1].cost
                    let times = num
                    if (i < makeDatas.length) {
                        let t = makeDatas[i].times
                        if (num >= t) {
                            times = t
                        }
                    }
                    for (let cost of costs) {
                        let key = cost.id
                        if (!costMap[key]) costMap[key] = 0
                        costMap[key] += cost.num * times
                    }
                    num -= times
                    if (num <= 0) break
                }
            }
        }
        // console.log(index, costMap)

        let roles = []
        for (let i = 0; i < roleCnt; i++) {
            let sum = 0
            for (let max of maxMap) {
                if (!max[i]) continue
                for (let needIndex in max[i]) {
                    let index = Number(needIndex)
                    let val = max[i][needIndex]
                    sum += val
                }
            }
            sum /= maxMap.length
            let rate = 1
            roles[i] = Math.max(0, sum/rate)
            roles[i] = util.toRound(roles[i], 3)
        }
        roles.sort((a, b) => b - a)
        for (let i = 0; i < roles.length; i++) {
            let ary = json["等级"+(i)]
            if (!ary) ary = []
            ary.push(roles[i])
            json["等级"+(i)] = ary
        }

        let exp = roles.reduce((a, b) => a + b) / roleCnt
        console.log(util.toRound(exp, 3), util.toRound(exp - preExp, 3), roles)
        json["平均等级"].push(exp)
        json["差值"].push(exp - preExp)
        preExp = exp


        for (let key in costMap) {
            if (Number(key) > 0) {
                json["宝石" + key].push(costMap[key])
            }
            else {
                json["星尘"].push(costMap[key])
            }
        }
    }

    toExcel(json)
}

let toExcel = (data, fileName?) => {
    let sheet = { columns: [], content: [] }
    let keyMap = {}
    let flat = (obj, path, res) => {
        if (typeof obj == 'object' && obj != null) {
            if (Array.isArray(obj)) {
                for (let val of obj) {
                    flat(val, path, res)
                }
            }
            else {

                for (let key in obj) {
                    if (path != "") {
                        flat(obj[key], `${path}-${key}`, res)
                    }
                    else {
                        flat(obj[key], `${key}`, res)
                    }
                }
            }
        }
        else {
            keyMap[path] = path
            res[path] = obj
        }
    }
    let keys = Object.keys(data)
    let count = data[keys[0]].length
    for (let i = 0; i < count; i++) {
        let res = {}
        for (let key in data) {
            flat(data[key][i], key, res)
        }
        sheet.content.push(res)
    }

    sheet.columns = Object.keys(keyMap).sort((key1, key2) => {
        return keys.findIndex(k => key1.includes(k)) - keys.findIndex(k => key2.includes(k))
    }).map(key => {
        return { label: key, value: key }
    })
    let settings = {
        fileName: !!fileName ? fileName : "./output/EquipLevelTest", // Name of the resulting spreadsheet
        extraLength: 3, // A bigger number means that columns will be wider
        writeMode: "writeFile", // The available parameters are 'WriteFile' and 'write'. This setting is optional. Useful in such cases https://docs.sheetjs.com/docs/solutions/output#example-remote-file
        writeOptions: {}, // Style options from https://docs.sheetjs.com/docs/api/write-options
    }
    xlsx([sheet], settings)
}

testEquipLv()