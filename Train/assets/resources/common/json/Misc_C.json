{"refreshTime": "05:00", "refreshTimePm": "17:00", "planetMoveTimeToStoneUnit": 5, "towerPower": {"max": 5, "recoverSpeed": 1}, "jackpotHistoryMaxNum": 30, "jackpotDailyMaxNum": 10000, "jackpot": {"mustGetCharacter": [1007, "1024,1035", 1015], "diffNum": 7}, "jackpotPointsConvert": 10, "jackpotPointsGet": 1000, "jackPotPrice": 300, "jackPotDiscount": 1, "ticket": {"mergeCnt": 10}, "speedUp": {"max": 30, "unlockAutoCost": [{"type": 1, "num": 200}], "autoMax": 30, "recoverFree": 0, "recoverSpeed": 0, "recoverBuy": 10, "buyTime": [1], "buyPrice": [1], "recoverEnergy": 0, "s3": 60, "s5": 3, "s6": 5, "s8": 5}, "toolPnlMine": [{"type": 1, "id": 1001}, {"type": 2, "id": 2001}, {"type": 3, "id": 3001}, {"type": 4, "id": 41140}], "toolInitial": [{"type": 1, "lv": 1}, {"type": 2, "lv": 1}, {"type": 3, "lv": 0}, {"type": 4, "lv": 48}], "wanted": {"refreshCost": 10, "initialLevel": [1, 2, 1, 3, 2, 3]}, "qteDamage": {"blue": 1, "purple": 2, "gold": 3}, "character": {"moveSpeed": 300, "rageModeSpeed": 1200}, "trainGoodsLikeWeightRate": 9, "timeTransition": 30, "guide": {"letsgoPlotKey": "guidePlot_key_1_3", "endingTimeId": 806, "taskId": ["2001_2"], "enterTeamTips": "1005-1-2", "failTips": "1005-1-1", "failTipsClose": 1606, "headstockStarNum": 5}, "INITIAL": [{"type": 10, "id": 1005, "num": 1}], "renamed": {"freeNum": 1, "buyPrice": 100}, "logout": {"waitingTime": 7}, "iconPer": {"star": 15, "electricity": 10, "water": 10, "vitality": 10}, "checkInOutputRate": 0.05, "workOutputRate": 0.05, "instanceTimes": 3, "instanceFinish": {"time": 1}, "stores": [{"refreshNum": 1, "refreshPrice": 100}, {"refreshTime": 15, "refreshPrice": 1000}, {"refreshTime": 7, "refreshPrice": 1000}, {"refreshTime": 1, "refreshPrice": -1}], "rebirthCD": 30, "rebirthFinish": {"time": 1}, "resonance": {"cd": 24, "cdCost": 100}, "lvAttrRatio": 0.2, "starLvAttrRatio": 0.1, "field": {"foodLandUnlockCost": [50000, 100000, 200000, 300000, 600000, 1200000], "fruitLandUnlockCost": [80000, 250000, 1250000], "foodLandUnlock": 2, "fruitLandUnlock": 1}, "fertilizer": [{"effect": 120, "id": 6001}, {"effect": 240, "id": 6002}, {"effect": 480, "id": 6003}, {"effect": -1, "id": 6004}], "talent": {"maxLevelBase": 5, "maxLevelRate": 0.2}, "transport": {"count": 6, "timeRate": 4, "d0": 60, "d1": 30, "d2": 10, "rareExp": 10}, "blackHole": {"baseAttr": 3}, "fragUp": [3, 3, 3], "ore": {"breakMaxNum": 40, "monsterMinLevel": 20, "monsterDepthRate": 30, "bossCondition": 2, "treasureValue": 4, "treasureValueMax": 0.8, "landValue": 10, "numWeight": [100, 70, 30], "terrian": [{"type": 0, "weight": 40}, {"type": 1, "weight": 13}, {"type": 2, "weight": 7}, {"type": 3, "weight": 5}, {"type": 4, "weight": 35}], "unlockAdd": 3}, "equip": {"sell": 0.15}, "dailyTask": {"targetMinNum": 1, "targetMaxNum": 3, "count": 6, "bigReward": [{"type": 1, "num": 60}]}, "collect": {"map": {"width": 5376, "height": 2484}}, "arrest": {"place": [{"type": 1, "weight": 1}, {"type": 2, "weight": 2}], "time": [{"type": 0, "weight": 8}, {"type": 1, "weight": 1}, {"type": 2, "weight": 1}]}, "spaceStone": {"cost": 10}, "pvp": [{"ticketMax": 5, "duration": 14, "reward": [{"min": 1, "max": 1, "num": 100}, {"min": 2, "max": 2, "num": 50}, {"min": 3, "max": 3, "num": 40}, {"min": 4, "max": 10, "num": 30}, {"min": 11, "max": -1, "num": 20}], "seasonReward": [{"min": 1, "max": 1, "num": 700}, {"min": 2, "max": 2, "num": 350}, {"min": 3, "max": 3, "num": 280}, {"min": 4, "max": 10, "num": 210}, {"min": 11, "max": -1, "num": 140}]}, {"ticketMax": 3}, {"ticketMax": 5}], "deepExplore": {"spaceshipCost": [800, 2500], "spaceshipMaxNum": 3}, "ad": [{"count": 10, "resetType": "day"}, {"count": 3, "resetType": "day"}], "profileBranch": {"maxEnergy": 3, "recoverTime": 600}, "timeBox": [{"time": 4, "reward": [{"num": 100, "type": 1}], "id": 601}, {"time": 8, "reward": [{"num": 200, "type": 1}], "id": 602}, {"time": 12, "reward": [{"num": 400, "type": 1}], "id": 603}], "randomBox": [{"id": 701, "rewardRandom": [{"type": 2, "num": 1000, "weight": 50}, {"type": 3, "num": 1000, "weight": 50}], "reward": [{"type": 1, "num": 50, "weight": 6}, {"type": 1, "num": 100, "weight": 3}, {"type": 1, "num": 300, "weight": 1}]}], "trainActivity": {"dailyCntMin": 5, "dailyCntMax": 7, "rewardRandom": [{"type": 2, "base": 0, "rate": 7.5, "weight": 1}, {"type": 3, "base": 0, "rate": 4.5, "weight": 1}, {"type": 11, "id": 5, "base": 0, "rate": 4.5, "weight": 1}, {"type": 11, "id": 6, "base": 0, "rate": 4.5, "weight": 1}, {"type": 11, "id": 18, "base": 0, "rate": 4.5, "weight": 1}]}}