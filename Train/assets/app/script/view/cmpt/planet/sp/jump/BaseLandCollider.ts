import DieLandCollider from "./DieLandCollider"
import HeroShadowLandCollider from "./HeroShadowLandCollider"
import NormalLandCollider from "./NormalLandCollider"
import RebirthLandCollider from "./RebirthLandCollider"
import WaterLandCollider from "./WaterLandCollider"

export const HERO_FOOT_WIDTH = 20 // Hero脚部碰撞宽度（左右各延伸20px）
export enum LandType {
    None = "none",
    Normal = "normal",
    Die = "die",
    Water = "water",
    Rebirth = "rebirth",
    Shadow = "shadow",
}

// Hero轨迹信息
export interface HeroTrajectory {
    // 左脚线段位置
    leftFoot: { start: cc.Vec2, end: cc.Vec2 }
    // 右脚线段位置
    rightFoot: { start: cc.Vec2, end: cc.Vec2 }
    // 世界矩形
    worldPoints: cc.Vec2[]
    offset: cc.Vec2
}

// 碰撞结果
export interface CollisionResult {
    point: cc.Vec2      // 碰撞点
    distance: number    // 碰撞距离（用于选择最近碰撞）
    normal?: cc.Vec2    // 碰撞法线（可选）
    waterSpeed?: number // 水流速度
}

export interface LandCollider extends cc.Component {
    type: LandType
    node: cc.Node
    index: number

    intersectWith: (trajectory: HeroTrajectory) => CollisionResult | null
    isNeedHeroCollider(): boolean
    getLeftMax(): number
    getRightMax(): number
}

export default class BaseLandCollider extends cc.Component implements LandCollider {
    type: LandType = LandType.None
    node: cc.Node = null
    index: number = 0

    public intersectWith(_trajectory: HeroTrajectory): CollisionResult | null { return null }

    public isNeedHeroCollider(): boolean { return false }
    public getLeftMax(): number { throw new Error("Method not implemented.") }
    public getRightMax(): number { throw new Error("Method not implemented.") }
}


export class LandColliderFactory {
    private static colliderMap = new Map<LandType, new () => LandCollider>([
        [LandType.Normal, NormalLandCollider],
        [LandType.Shadow, HeroShadowLandCollider],
        [LandType.Die, DieLandCollider],
        [LandType.Water, WaterLandCollider],
        [LandType.Rebirth, RebirthLandCollider],
    ])

    static createCollider(point: cc.Node): LandCollider {
        let landType = this.determineLandType(point)
        const ColliderClass = this.colliderMap.get(landType) || NormalLandCollider
        const cmpt = point.addComponent(ColliderClass)
        return cmpt
    }

    private static determineLandType(point: cc.Node): LandType {
        const nodeName = point.name.toLowerCase()
        if (nodeName.includes(LandType.Shadow)) return LandType.Shadow
        if (nodeName.includes(LandType.Die)) return LandType.Die
        if (nodeName.includes(LandType.Water)) return LandType.Water
        if (nodeName.includes(LandType.Rebirth)) return LandType.Rebirth

        return LandType.Normal
    }
}